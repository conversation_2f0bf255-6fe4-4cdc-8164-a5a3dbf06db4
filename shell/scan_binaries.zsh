#!/bin/zsh
echo " "
# Get the directory where the script lives
SCRIPT_DIR="${0:A:h}"

# Path to the output file for problematic binaries
BAD_FILE="${SCRIPT_DIR:h}/bad_binaries_zsh.txt"

# Empty the bad interpreter file at the start
: > "$BAD_FILE"

# Progress bar settings
bar_width=40

# Counter for processed files and bad binaries
count=0
bad_count=0

# Get the list of regular, executable files in /usr/local/bin
files=()
for file in /usr/local/bin/*; do
  [[ -f "$file" && -x "$file" ]] && files+=("$file")
done
total=${#files[@]}

# Function to print the progress bar (default color)
print_progress() {
  local progress=$((count * bar_width / total))
  local percent=$((count * 100 / total))
  local bar=""
  for ((i=1; i<=bar_width; i++)); do
    if ((i <= progress)); then
      bar+="#"
    else
      bar+=" "
    fi
  done
  printf "\r[%s] %3d%% (%d/%d)" "$bar" $percent $count $total
}

# Loop through each executable file
for file in "${files[@]}"; do
  ((count++))  # Increment processed file count

  # Print progress bar
  print_progress

  # Try to run with --help to avoid launching GUI apps
  "$file" --help >/dev/null 2>&1
  if [[ $? -eq 0 ]]; then
    continue
  fi

  # Try to run with -h to avoid launching GUI apps
  "$file" -h >/dev/null 2>&1
  if [[ $? -eq 0 ]]; then
    continue
  fi

  # Try to run directly, but limit execution time to 2 seconds
  ( "$file" >/dev/null 2>&1 ) &
  PID=$!
  sleep 2
  if kill -0 $PID 2>/dev/null; then
    kill $PID 2>/dev/null
    wait $PID 2>/dev/null
  fi

  # Capture error message if any
  ERROR_MSG=$( "$file" 2>&1 1>/dev/null )

  # Check for "bad interpreter" or "No such file or directory" in error message
  if [[ "$ERROR_MSG" == *"bad interpreter"* || "$ERROR_MSG" == *"No such file or directory"* ]]; then
    echo "$file" >> "$BAD_FILE"
    ((bad_count++))  # Increment bad binary count
  fi
done

# Print final progress bar at 100%
print_progress
printf "\n\nFound %d bad binaries. Check %s\n" $bad_count "$BAD_FILE"