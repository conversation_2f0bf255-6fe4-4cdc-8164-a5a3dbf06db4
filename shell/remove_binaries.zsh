#!/bin/zsh

# Get the directory where the script lives
SCRIPT_DIR="${0:A:h}"
BAD_FILE="$SCRIPT_DIR/bad._interpreter.txt"

if [[ ! -f "$BAD_FILE" ]]; then
  echo "Error: $BAD_FILE not found."
  exit 1
fi

removed_count=0
skipped_count=0

while IFS= read -r file; do
  if [[ -f "$file" ]]; then
    rm "$file"
    if [[ $? -eq 0 ]]; then
      echo "Removed: $file"
      ((removed_count++))
    else
      echo "Failed to remove: $file"
      ((skipped_count++))
    fi
  else
    echo "Skipped (not found): $file"
    ((skipped_count++))
  fi
done < "$BAD_FILE"

echo "Done. Removed $removed_count files. Skipped $skipped_count files."