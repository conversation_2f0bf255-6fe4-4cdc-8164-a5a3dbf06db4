#!/usr/bin/env python3

"""Async scan all binaries for bad interpreters."""

from asyncio import (
    Semaphore,
    as_completed,
    create_subprocess_exec,
    subprocess,
    wait_for,
)
from asyncio import TimeoutError as ATimeout
from asyncio import run as a_run
from contextlib import suppress
from os import X_OK, access
from pathlib import Path
from typing import Any

from aiofiles import open as aio_open
from rich import print as richp
from send2trash import send2trash
from tqdm.asyncio import tqdm

SYSTEM_EXCEPTIONS = (
    FileNotFoundError,
    PermissionError,
    OSError,
    RuntimeError,
    TimeoutError,
    SystemError,
    FileExistsError,
    ATimeout,
)

SCRIPT_DIR = Path(__file__).resolve().parent.parent
BAD_BIN = SCRIPT_DIR / "bad_binaries_1.txt"
BIN_DIR = Path("/usr/sbin")
# BIN_DIR = Path("/usr/local/bin")
ERR_MSSGS = ["bad interpreter", "No such file or directory"]


def tqdm_args(total: int, desc: str, *, scale: bool = True) -> dict[str, Any]:
    bar_format = (
        "{desc}: {percentage:3.0f}%|{bar}| {n_fmt}/{total_fmt}, {elapsed}<{remaining}, "
        "{rate_fmt} {postfix}"
    )
    return {
        "desc": desc,
        "total": total,
        "unit_scale": scale,
        "ncols": 100,
        "colour": "#6bedac",
        "unit": "B",
        "disable": not total,
        "bar_format": bar_format,
        "miniters": 0,
        "mininterval": 0.1,
        "unit_divisor": 1024,
    }


async def collect_binaries() -> list[Path]:
    """Collect all binaries in /usr/local/bin."""

    # it's important to check if the file's permission(X_OK) before checking if it's a file
    return sorted(f for f in BIN_DIR.iterdir() if access(f, X_OK) and f.is_file())


async def run_command(cmd: list[str], timeout: int = 1) -> tuple[int, str]:
    """Run command asynchronously and return returncode and stderr."""

    try:
        proc = await create_subprocess_exec(
            *cmd,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.PIPE,
        )
        _, stderr = await wait_for(proc.communicate(), timeout=timeout)
        return proc.returncode, stderr.decode(errors="ignore") if stderr else ""
    except SYSTEM_EXCEPTIONS as e:
        return 1, str(e)


async def check_binary(file: Path, semaphore: Semaphore) -> tuple[bool, Path]:
    """Check if binary has bad interpreter."""

    async with semaphore:
        # Try --help, -h first
        for flag in ["--help", "-h"]:
            returncode, _ = await run_command([str(file), flag])
            if returncode == 0:  # No error
                return False, file

        # Try running directly
        _, stderr = await run_command([str(file)])
        return any(msg in stderr for msg in ERR_MSSGS), file


async def scan_binaries(binaries: list[Path]) -> None:
    """Scan all binaries concurrently for bad interpreters."""

    bad_bins = []
    semaphore = Semaphore(10)  # Limit concurrent processes

    pbar = tqdm(**tqdm_args(len(binaries), "Scanning binaries", scale=False))
    tasks = [check_binary(file, semaphore) for file in binaries]

    async with aio_open(BAD_BIN, "w") as f:
        for task in as_completed(tasks):
            error, file = await task
            if error:
                await f.write(f"{file}\n")
                bad_bins.append(file)
            pbar.update(1)

    pbar.close()
    mssg = "No bad binarie(s) found."
    if bad_bins:
        mssg = f"Found {len(bad_bins)} bad binarie(s). Check {BAD_BIN}"
    richp(f"\n[b i yellow]{mssg}[/b i yellow]")


def remove_all() -> None:
    """Remove all bad binaries."""

    success = 0
    try:
        with BAD_BIN.open("r+") as f:
            files = f.read().splitlines()
            for file in files:
                send2trash(file)
                success += 1
    except SYSTEM_EXCEPTIONS as e:
        richp(f"\n[b i red]Error: {e}[/b i red]")

    richp(f"\n[b i green]{success} bad binarie(s) removed.[b i green]")


async def main() -> None:
    binaries = await collect_binaries()
    await scan_binaries(binaries)
    # remove_all()


if __name__ == "__main__":
    richp("\n[b i green]Scanning binaries...[/b i green]\n")
    a_run(main())
    richp("\n[b i green]Done[/b i green]\n")
