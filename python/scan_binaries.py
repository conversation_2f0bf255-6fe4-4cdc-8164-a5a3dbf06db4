#!/usr/bin/env python3

"""Scan all binaries in /usr/local/bin for bad interpreters."""

from contextlib import suppress
from os import X_OK, access
from pathlib import Path
from subprocess import DEVNULL, PIPE, run
from typing import Any

from rich import print as richp
from send2trash import send2trash
from tqdm import tqdm

SYSTEM_EXCEPTIONS = (
    FileNotFoundError,
    PermissionError,
    OSError,
    RuntimeError,
    TimeoutError,
    SystemError,
    FileExistsError,
)

BIN_DIR = Path("/usr/sbin")
# BIN_DIR = Path("/usr/local/bin")
SCRIPT_DIR = Path(__file__).resolve().parent.parent
BAD_BIN = SCRIPT_DIR / "bad_binaries_2.txt"
ERR_MSSG = ["bad interpreter", "No such file or directory"]


def collect_binaries() -> list[Path]:
    """Collect all binaries in /usr/local/bin."""

    # it's important to check if the file's permission(X_OK) before checking if it's a file
    return sorted(f for f in BIN_DIR.iterdir() if access(f, X_OK) and f.is_file())


def tqdm_args(total: int, desc: str, *, scale: bool = True) -> dict[str, Any]:
    bar_format = (
        "{desc}: {percentage:3.0f}%|{bar}| {n_fmt}/{total_fmt}, {elapsed}<{remaining}, "
        "{rate_fmt} {postfix}"
    )

    return {
        "desc": desc,
        "total": total,
        "unit_scale": scale,
        "ncols": 100,
        "colour": "#6bedac",
        "unit": "B",
        "disable": not total,
        "bar_format": bar_format,
        "miniters": 0,
        "mininterval": 0.1,
        "unit_divisor": 1024,
    }


def zsh_progress(count: int, total: int, bar_width: int = 40) -> None:
    progress = int(count * bar_width / total)
    percent = int(count * 100 / total)
    bar = "#" * progress + " " * (bar_width - progress)
    print(f"\r[{bar}] {percent:3d}% ({count}/{total})", end="", flush=True)


def run_command(cmd: list[str]) -> str:
    """Run a command and return the output."""

    try:
        proc = run(  # noqa: S603
            cmd,
            stdout=DEVNULL,
            stderr=PIPE,
            timeout=2,
            check=False,
            shell=False,  # Explicitly set shell=False for security
        )
        returncode = proc.returncode
        if err := proc.stderr:
            return returncode, err.decode(errors="ignore")
        return returncode, ""  # noqa: TRY300
    except SYSTEM_EXCEPTIONS as e:
        return 1, str(e)


def check_binary(file: Path) -> tuple[bool, Path]:
    """Check if binary has bad interpreter."""

    # Try --help, -h first
    for flag in ["--help", "-h"]:
        if run_command([str(file), flag])[0] == 0:  # No error
            return False, file

    # Try running directly
    _, stderr = run_command([str(file)])
    return any(msg in stderr for msg in ERR_MSSG), file


def scan_binaries(binaries: list[Path]) -> None:
    """Scan all binaries in /usr/local/bin for bad interpreters."""

    bad_bins: list[Path] = []
    pbar = tqdm(**tqdm_args(len(binaries), "Scanning binaries", scale=False))

    with open(BAD_BIN, "w") as f:
        for binary in binaries:
            error, file = check_binary(binary)
            if error:
                f.write(f"{file}\n")
                bad_bins.append(file)
            pbar.update(1)

    pbar.close()
    mssg = "No bad binarie(s) found."
    if bad_bins:
        mssg = f"Found {len(bad_bins)} bad binarie(s). Check {BAD_BIN}"
    richp(f"\n[b i yellow]{mssg}[/b i yellow]")


def remove_all() -> None:
    """Remove all bad binaries from /usr/local/bin."""

    success = 0
    try:
        with BAD_BIN.open("r+") as f:
            files = f.read().splitlines()
            for file in files:
                send2trash(file)
                success += 1
    except SYSTEM_EXCEPTIONS as e:
        richp(f"\n[b i red]Error: {e}[/b i red]")

    richp(f"\n[b i green]{success} bad binarie(s) removed.[b i green]")


def main() -> None:
    binaries = collect_binaries()
    scan_binaries(binaries)
    # remove_all()


if __name__ == "__main__":
    richp("\n[b i green]Scanning binaries...[/b i green]\n")
    main()
    richp("\n[b i green]Done[/b i green]\n")
